using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// 存储实例类，代表一个独立的存储域
    /// 每个实例拥有独立的缓存、设置和文件
    /// </summary>
    public class StorageInstance
    {
        #region 私有字段

        private readonly string _instanceId;
        private readonly StorageCache _cache;
        private readonly StorageSettings _settings;
        private readonly object _lockObject = new object(); // 内存缓存操作锁
        private readonly ReaderWriterLockSlim _fileOperationLock = new ReaderWriterLockSlim(); // 文件操作读写锁
        private bool _isDisposed = false;

        // 用于跟踪当前文件操作状态的字段
        private volatile bool _isFileOperationInProgress = false;
        private volatile string _currentFileOperation = string.Empty;

        #endregion

        #region 属性

        /// <summary>
        /// 实例标识符
        /// </summary>
        public string InstanceId
        {
            get { return _instanceId; }
        }

        /// <summary>
        /// 存储设置
        /// </summary>
        public StorageSettings Settings
        {
            get { return _settings; }
        }

        /// <summary>
        /// 是否已被释放
        /// </summary>
        public bool IsDisposed
        {
            get { return _isDisposed; }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 创建存储实例
        /// </summary>
        /// <param name="instanceId">实例标识符</param>
        /// <param name="settings">存储设置</param>
        internal StorageInstance(string instanceId, StorageSettings settings)
        {
            _instanceId = instanceId;
            _settings = settings;
            _cache = new StorageCache();

            NLogger.Log("Storage instance created: {0}", arg0: instanceId);
        }

        #endregion

        #region 数据操作方法

        /// <summary>
        /// 保存数据到内存缓存
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        public void Set<T>(string key, T value)
        {
            if (_isDisposed)
            {
                NLogger.LogError("Instance [{0}] is disposed", arg0: _instanceId);
                return;
            }

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return;
            }

            lock (_lockObject)
            {
                _cache.Set(key, value);
                NLogger.Log("Instance [{0}] - Key: {1}, Type: {2}, Value: {3}",
                    arg0: _instanceId, arg1: key, arg2: typeof(T).Name, arg3: FormatValueForLog(value));
            }
        }

        /// <summary>
        /// 尝试从内存缓存获取数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">输出的数据值</param>
        /// <returns>是否成功获取到值</returns>
        public bool TryGet<T>(string key, out T value)
        {
            value = default;

            if (_isDisposed)
            {
                NLogger.LogError("Instance [{0}] is disposed", arg0: _instanceId);
                return false;
            }

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return false;
            }

            lock (_lockObject)
            {
                return _cache.TryGet(key, out value);
            }
        }

        /// <summary>
        /// 尝试从内存缓存获取数据，如果不存在则返回默认值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">输出的数据值</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>是否成功获取到值</returns>
        public bool TryGet<T>(string key, out T value, T defaultValue)
        {
            bool success = TryGet(key, out value);
            if (!success)
            {
                value = defaultValue;
            }
            return success;
        }

        /// <summary>
        /// 检查键是否存在
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否存在</returns>
        public bool ContainsKey(string key)
        {
            if (_isDisposed)
            {
                NLogger.LogError("Instance [{0}] is disposed", arg0: _instanceId);
                return false;
            }

            if (string.IsNullOrEmpty(key))
                return false;

            lock (_lockObject)
            {
                return _cache.ContainsKey(key);
            }
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否删除成功</returns>
        public bool Remove(string key)
        {
            if (_isDisposed)
            {
                NLogger.LogError("Instance [{0}] is disposed", arg0: _instanceId);
                return false;
            }

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return false;
            }

            lock (_lockObject)
            {
                return _cache.Remove(key);
            }
        }

        /// <summary>
        /// 清空所有数据
        /// </summary>
        public void Clear()
        {
            if (_isDisposed)
            {
                NLogger.LogError("Instance [{0}] is disposed", arg0: _instanceId);
                return;
            }

            lock (_lockObject)
            {
                _cache.Clear();
                NLogger.Log("Instance [{0}] - All data cleared from cache", arg0: _instanceId);
            }
        }

        /// <summary>
        /// 获取所有键
        /// </summary>
        /// <returns>键的集合</returns>
        public ICollection<string> GetAllKeys()
        {
            if (_isDisposed)
            {
                NLogger.LogError("Instance [{0}] is disposed", arg0: _instanceId);
                return null;
            }

            lock (_lockObject)
            {
                return _cache.Keys;
            }
        }

        #endregion

        #region 文件操作方法

        /// <summary>
        /// 异步保存所有数据到文件
        /// </summary>
        /// <param name="callback">完成回调</param>
        public void SaveToFileAsync(FileOperationCallback callback = null)
        {
            if (_isDisposed)
            {
                NLogger.LogError("Instance [{0}] is disposed", arg0: _instanceId);
                callback?.Invoke(false, "Instance is disposed");
                return;
            }

            Dictionary<string, object> data;
            bool isDirty;

            // 获取缓存数据（使用内存锁）
            lock (_lockObject)
            {
                isDirty = _cache.IsDirty;
                if (!isDirty)
                {
                    NLogger.Log("Instance [{0}] - Cache is not dirty, skipping file save operation", arg0: _instanceId);
                    callback?.Invoke(true);
                    return;
                }
                data = _cache.GetAllData();
            }

            // 记录文件操作状态
            _isFileOperationInProgress = true;
            _currentFileOperation = "SaveAsync";

            NLogger.Log("Instance [{0}] - Starting async save operation", arg0: _instanceId);

            // 使用读写锁的写锁进行文件保存
            SaveDataToFileAsyncWithWriteLock(data, _settings, _fileOperationLock, (success, errorMessage) =>
            {
                if (success)
                {
                    lock (_lockObject)
                    {
                        _cache.MarkAsSaved();
                    }
                    NLogger.Log("Instance [{0}] - Data saved successfully", arg0: _instanceId);
                }
                else
                {
                    NLogger.LogError("Instance [{0}] - Failed to save data: {1}", arg0: _instanceId, arg1: errorMessage);
                }

                // 清除文件操作状态
                _isFileOperationInProgress = false;
                _currentFileOperation = string.Empty;

                callback?.Invoke(success, errorMessage);
            });
        }

        /// <summary>
        /// 异步从文件加载数据
        /// </summary>
        /// <param name="callback">完成回调</param>
        public void LoadFromFileAsync(FileReadCallback callback = null)
        {
            if (_isDisposed)
            {
                NLogger.LogError("Instance [{0}] is disposed", arg0: _instanceId);
                callback?.Invoke(false, null, "Instance is disposed");
                return;
            }

            // 记录文件操作状态
            _isFileOperationInProgress = true;
            _currentFileOperation = "LoadAsync";

            NLogger.Log("Instance [{0}] - Starting async load operation", arg0: _instanceId);

            // 使用读写锁的读锁进行文件加载
            LoadDataFromFileAsyncWithReadLock(_settings, _fileOperationLock, (success, data, errorMessage) =>
            {
                if (success && data != null)
                {
                    lock (_lockObject)
                    {
                        _cache.LoadFromDictionary(data);
                    }
                    NLogger.Log("Instance [{0}] - Data loaded successfully, {1} items", arg0: _instanceId, arg1: data.Count);
                }
                else
                {
                    NLogger.LogError("Instance [{0}] - Failed to load data: {1}", arg0: _instanceId, arg1: errorMessage);
                }

                // 清除文件操作状态
                _isFileOperationInProgress = false;
                _currentFileOperation = string.Empty;

                callback?.Invoke(success, data, errorMessage);
            });
        }

        /// <summary>
        /// 同步保存数据到文件
        /// </summary>
        /// <returns>是否成功</returns>
        public bool SaveToFileSync()
        {
            if (_isDisposed)
            {
                NLogger.LogError("Instance [{0}] is disposed", arg0: _instanceId);
                return false;
            }

            Dictionary<string, object> data;
            bool isDirty;

            // 获取缓存数据（使用内存锁）
            lock (_lockObject)
            {
                isDirty = _cache.IsDirty;
                if (!isDirty)
                {
                    NLogger.Log("Instance [{0}] - Cache is not dirty, skipping file save operation", arg0: _instanceId);
                    return true;
                }
                data = _cache.GetAllData();
            }

            // 记录文件操作状态
            _isFileOperationInProgress = true;
            _currentFileOperation = "SaveSync";

            NLogger.Log("Instance [{0}] - Starting sync save operation", arg0: _instanceId);

            bool success = false;
            try
            {
                // 使用读写锁的写锁进行文件保存
                _fileOperationLock.EnterWriteLock();
                try
                {
                    success = SaveDataToFileSync(data, _settings);
                }
                finally
                {
                    _fileOperationLock.ExitWriteLock();
                }

                if (success)
                {
                    lock (_lockObject)
                    {
                        _cache.MarkAsSaved();
                    }
                    NLogger.Log("Instance [{0}] - Data saved synchronously", arg0: _instanceId);
                }
                else
                {
                    NLogger.LogError("Instance [{0}] - Failed to save data synchronously", arg0: _instanceId);
                }
            }
            finally
            {
                // 清除文件操作状态
                _isFileOperationInProgress = false;
                _currentFileOperation = string.Empty;
            }

            return success;
        }

        /// <summary>
        /// 同步从文件加载数据
        /// </summary>
        /// <returns>是否成功</returns>
        public bool LoadFromFileSync()
        {
            if (_isDisposed)
            {
                NLogger.LogError("Instance [{0}] is disposed", arg0: _instanceId);
                return false;
            }

            // 记录文件操作状态
            _isFileOperationInProgress = true;
            _currentFileOperation = "LoadSync";

            NLogger.Log("Instance [{0}] - Starting sync load operation", arg0: _instanceId);

            bool success = false;
            try
            {
                // 使用读写锁的读锁进行文件加载
                _fileOperationLock.EnterReadLock();
                try
                {
                    var result = LoadDataFromFileSync(_settings);
                    if (result.success && result.data != null)
                    {
                        lock (_lockObject)
                        {
                            _cache.LoadFromDictionary(result.data);
                        }
                        NLogger.Log("Instance [{0}] - Data loaded synchronously, {1} items", arg0: _instanceId, arg1: result.data.Count);
                        success = true;
                    }
                    else
                    {
                        NLogger.LogError("Instance [{0}] - Failed to load data synchronously: {1}", arg0: _instanceId, arg1: result.errorMessage);
                        success = false;
                    }
                }
                finally
                {
                    _fileOperationLock.ExitReadLock();
                }
            }
            finally
            {
                // 清除文件操作状态
                _isFileOperationInProgress = false;
                _currentFileOperation = string.Empty;
            }

            return success;
        }

        #endregion

        #region 内部文件操作方法

        /// <summary>
        /// 使用读写锁的写锁异步保存数据到文件
        /// </summary>
        private static void SaveDataToFileAsyncWithWriteLock(Dictionary<string, object> data, StorageSettings settings, ReaderWriterLockSlim fileOperationLock, FileOperationCallback callback)
        {
            if (settings.Location == StorageSettings.StorageLocation.PlayerPrefs)
            {
                // PlayerPrefs必须在主线程操作，不需要文件锁
                SaveToPlayerPrefs(data, settings, callback);
            }
            else if (settings.Location == StorageSettings.StorageLocation.File)
            {
                // 文件操作在后台线程执行，使用写锁
                SaveToFileInBackgroundWithWriteLock(data, settings, fileOperationLock, callback);
            }
            else
            {
                callback?.Invoke(false, $"Storage location {settings.Location} is not supported for writing");
            }
        }

        /// <summary>
        /// 使用读写锁的读锁异步从文件加载数据
        /// </summary>
        private static void LoadDataFromFileAsyncWithReadLock(StorageSettings settings, ReaderWriterLockSlim fileOperationLock, FileReadCallback callback)
        {
            if (settings.Location == StorageSettings.StorageLocation.PlayerPrefs)
            {
                // PlayerPrefs必须在主线程操作，不需要文件锁
                LoadFromPlayerPrefs(settings, callback);
            }
            else if (settings.Location == StorageSettings.StorageLocation.File)
            {
                // 文件操作在后台线程执行，使用读锁
                LoadFromFileInBackgroundWithReadLock(settings, fileOperationLock, callback);
            }
            else
            {
                callback?.Invoke(false, null, $"Storage location {settings.Location} is not supported for reading");
            }
        }

        /// <summary>
        /// 异步保存数据到文件
        /// </summary>
        private static void SaveDataToFileAsync(Dictionary<string, object> data, StorageSettings settings, FileOperationCallback callback)
        {
            SaveDataToFileAsync(data, settings, null, callback);
        }

        /// <summary>
        /// 异步保存数据到文件（带锁版本）
        /// </summary>
        private static void SaveDataToFileAsync(Dictionary<string, object> data, StorageSettings settings, object fileOperationLock, FileOperationCallback callback)
        {
            if (settings.Location == StorageSettings.StorageLocation.PlayerPrefs)
            {
                // PlayerPrefs必须在主线程操作
                SaveToPlayerPrefs(data, settings, callback);
            }
            else if (settings.Location == StorageSettings.StorageLocation.File)
            {
                // 文件操作在后台线程执行，使用锁
                SaveToFileInBackground(data, settings, fileOperationLock, callback);
            }
            else
            {
                callback?.Invoke(false, $"Storage location {settings.Location} is not supported for writing");
            }
        }

        /// <summary>
        /// 异步从文件加载数据
        /// </summary>
        private static void LoadDataFromFileAsync(StorageSettings settings, FileReadCallback callback)
        {
            if (settings.Location == StorageSettings.StorageLocation.PlayerPrefs)
            {
                // PlayerPrefs必须在主线程操作
                LoadFromPlayerPrefs(settings, callback);
            }
            else if (settings.Location == StorageSettings.StorageLocation.File)
            {
                // 文件操作在后台线程执行
                LoadFromFileInBackground(settings, callback);
            }
            else
            {
                callback?.Invoke(false, null, $"Storage location {settings.Location} is not supported for reading");
            }
        }

        /// <summary>
        /// 同步保存数据到文件
        /// </summary>
        private static bool SaveDataToFileSync(Dictionary<string, object> data, StorageSettings settings)
        {
            try
            {
                // 使用Storage的核心方法处理数据序列化和加密
                string json = Storage.SerializeAndEncrypt(data, settings);

                // 保存到文件
                Storage.WriteToFileWithBackup(json, settings);

                return true;
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to save data synchronously: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return false;
            }
        }

        /// <summary>
        /// 同步从文件加载数据
        /// </summary>
        private static (bool success, Dictionary<string, object> data, string errorMessage) LoadDataFromFileSync(StorageSettings settings)
        {
            try
            {
                // 从文件读取
                string json = Storage.ReadFromFileWithRecovery(settings);

                if (string.IsNullOrEmpty(json))
                {
                    return (false, null, "File is empty or does not exist");
                }

                // 使用Storage的核心方法处理数据解密和反序列化
                var data = Storage.DecryptAndDeserialize(json, settings);

                return (true, data, null);
            }
            catch (Exception ex)
            {
                return (false, null, ex.Message);
            }
        }

        /// <summary>
        /// 在主线程中保存数据到PlayerPrefs
        /// </summary>
        private static void SaveToPlayerPrefs(Dictionary<string, object> data, StorageSettings settings, FileOperationCallback callback)
        {
            if (Thread.CurrentThread.ManagedThreadId != StorageManager.MainThreadId)
            {
                NLogger.LogError("Cannot access PlayerPrefs from a background thread");
                callback?.Invoke(false, "Cannot access PlayerPrefs from a background thread");
                return;
            }

            SaveToPlayerPrefsInternal(data, settings, callback);
        }

        /// <summary>
        /// PlayerPrefs保存的内部实现
        /// </summary>
        private static void SaveToPlayerPrefsInternal(Dictionary<string, object> data, StorageSettings settings, FileOperationCallback callback)
        {
            try
            {
                // 使用Storage的核心方法处理数据序列化和加密
                string json = Storage.SerializeAndEncrypt(data, settings);

                // 保存到PlayerPrefs
                PlayerPrefs.SetString(settings.FilePath, json);
                PlayerPrefs.Save();

                NLogger.Log("Saved {0} items to PlayerPrefs: {1}", arg0: data.Count, arg1: settings.FilePath);
                callback?.Invoke(true);
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to save data to PlayerPrefs: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                callback?.Invoke(false, ex.Message);
            }
        }

        /// <summary>
        /// 在后台线程中保存数据到文件
        /// </summary>
        private static void SaveToFileInBackground(Dictionary<string, object> data, StorageSettings settings, FileOperationCallback callback)
        {
            SaveToFileInBackground(data, settings, null, callback);
        }

        /// <summary>
        /// 在后台线程中保存数据到文件（带锁版本）
        /// 使用线程池优化性能，避免频繁创建和销毁线程的开销
        /// </summary>
        private static void SaveToFileInBackground(Dictionary<string, object> data, StorageSettings settings, object fileOperationLock, FileOperationCallback callback)
        {
            // 使用Task.Run利用线程池，提高资源利用率和性能
            Task.Run(() =>
            {
                // 如果提供了锁，使用锁进行同步
                if (fileOperationLock != null)
                {
                    lock (fileOperationLock)
                    {
                        SaveToFileInBackgroundInternal(data, settings, callback);
                    }
                }
                else
                {
                    SaveToFileInBackgroundInternal(data, settings, callback);
                }
            });
        }

        /// <summary>
        /// 后台文件保存的内部实现
        /// </summary>
        private static void SaveToFileInBackgroundInternal(Dictionary<string, object> data, StorageSettings settings, FileOperationCallback callback)
        {
            try
            {
                // 使用Storage的核心方法处理数据序列化和加密
                string json = Storage.SerializeAndEncrypt(data, settings);

                // 保存到文件
                Storage.WriteToFileWithBackup(json, settings);

                // 在主线程执行回调
                Storage.RunOnMainThread(() =>
                {
                    NLogger.Log("Saved {0} items to file: {1}", arg0: data.Count, arg1: settings.GetFullPath());
                    callback?.Invoke(true);
                });
            }
            catch (Exception ex)
            {
                // 在主线程执行错误回调
                Storage.RunOnMainThread(() =>
                {
                    NLogger.LogError("Failed to save data to file: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                    callback?.Invoke(false, ex.Message);
                });
            }
        }

        /// <summary>
        /// 从PlayerPrefs加载数据（主线程执行）
        /// </summary>
        private static void LoadFromPlayerPrefs(StorageSettings settings, FileReadCallback callback)
        {
            if (Thread.CurrentThread.ManagedThreadId != StorageManager.MainThreadId)
            {
                NLogger.LogError("Cannot access PlayerPrefs from a background thread");
                callback?.Invoke(false, null, "Cannot access PlayerPrefs from a background thread");
                return;
            }

            try
            {
                string json = PlayerPrefs.GetString(settings.FilePath, string.Empty);

                if (string.IsNullOrEmpty(json))
                {
                    NLogger.LogWarning("PlayerPrefs key is empty or does not exist: {0}", arg0: settings.FilePath);
                    callback?.Invoke(false, null, "PlayerPrefs key is empty or does not exist");
                    return;
                }

                // 使用Storage的核心方法处理数据解密和反序列化
                var data = Storage.DecryptAndDeserialize(json, settings);

                NLogger.Log("Loaded {0} items from PlayerPrefs: {1}", arg0: data.Count, arg1: settings.FilePath);
                callback?.Invoke(true, data);
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to load data from PlayerPrefs: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                callback?.Invoke(false, null, ex.Message);
            }
        }

        /// <summary>
        /// 在后台线程中从文件加载数据
        /// 使用线程池优化性能，避免频繁创建和销毁线程的开销
        /// </summary>
        private static void LoadFromFileInBackground(StorageSettings settings, FileReadCallback callback)
        {
            // 使用Task.Run利用线程池，提高资源利用率和性能
            Task.Run(() =>
            {
                Dictionary<string, object> data = null;
                bool success = false;
                string errorMessage = null;
                int itemCount = 0;

                try
                {
                    // 从文件读取
                    string json = Storage.ReadFromFileWithRecovery(settings);

                    if (string.IsNullOrEmpty(json))
                    {
                        success = false;
                        errorMessage = "File is empty or does not exist";
                    }
                    else
                    {
                        // 在后台线程完成解密和反序列化操作，避免主线程卡顿
                        NLogger.Log("Starting decryption and deserialization in background thread: {0}", arg0: settings.GetFullPath());
                        data = Storage.DecryptAndDeserialize(json, settings);
                        itemCount = data?.Count ?? 0;
                        success = true;
                        NLogger.Log("Background deserialization completed, {0} items processed: {1}", arg0: itemCount, arg1: settings.GetFullPath());
                    }
                }
                catch (Exception ex)
                {
                    success = false;
                    errorMessage = ex.Message;
                    NLogger.LogError("Background thread error during file load: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                }

                // 将最终结果传递到主线程，避免在主线程进行耗时的反序列化操作
                Storage.RunOnMainThread(() =>
                {
                    if (success)
                    {
                        NLogger.Log("Loaded {0} items from file: {1}", arg0: itemCount, arg1: settings.GetFullPath());
                        callback?.Invoke(true, data);
                    }
                    else
                    {
                        if (errorMessage == "File is empty or does not exist")
                        {
                            NLogger.LogWarning("File is empty or does not exist: {0}", arg0: settings.GetFullPath());
                        }
                        else
                        {
                            NLogger.LogError("Failed to load data from file: {0}", arg0: errorMessage);
                        }
                        callback?.Invoke(false, null, errorMessage);
                    }
                });
            });
        }

        /// <summary>
        /// 在后台线程中保存数据到文件（使用写锁）
        /// 使用线程池优化性能，避免频繁创建和销毁线程的开销
        /// </summary>
        private static void SaveToFileInBackgroundWithWriteLock(Dictionary<string, object> data, StorageSettings settings, ReaderWriterLockSlim fileOperationLock, FileOperationCallback callback)
        {
            // 使用Task.Run利用线程池，提高资源利用率和性能
            Task.Run(() =>
            {
                try
                {
                    NLogger.Log("Acquiring write lock for file operation: {0}", arg0: settings.GetFullPath());

                    // 获取写锁
                    fileOperationLock.EnterWriteLock();
                    try
                    {
                        NLogger.Log("Write lock acquired, starting file save: {0}", arg0: settings.GetFullPath());
                        SaveToFileInBackgroundInternal(data, settings, callback);
                    }
                    finally
                    {
                        fileOperationLock.ExitWriteLock();
                        NLogger.Log("Write lock released for file: {0}", arg0: settings.GetFullPath());
                    }
                }
                catch (Exception ex)
                {
                    // 在主线程执行错误回调
                    Storage.RunOnMainThread(() =>
                    {
                        NLogger.LogError("Failed to acquire write lock or save file: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                        callback?.Invoke(false, ex.Message);
                    });
                }
            });
        }



        /// <summary>
        /// 在后台线程中从文件加载数据（使用读锁）
        /// 使用线程池优化性能，避免频繁创建和销毁线程的开销
        /// </summary>
        private static void LoadFromFileInBackgroundWithReadLock(StorageSettings settings, ReaderWriterLockSlim fileOperationLock, FileReadCallback callback)
        {
            // 使用Task.Run利用线程池，提高资源利用率和性能
            Task.Run(() =>
            {
                Dictionary<string, object> data = null;
                bool success = false;
                string errorMessage = null;
                int itemCount = 0;

                try
                {
                    NLogger.Log("Acquiring read lock for file operation: {0}", arg0: settings.GetFullPath());

                    // 获取读锁
                    fileOperationLock.EnterReadLock();
                    try
                    {
                        NLogger.Log("Read lock acquired, starting file load: {0}", arg0: settings.GetFullPath());

                        // 从文件读取
                        string json = Storage.ReadFromFileWithRecovery(settings);

                        if (string.IsNullOrEmpty(json))
                        {
                            success = false;
                            errorMessage = "File is empty or does not exist";
                        }
                        else
                        {
                            // 在后台线程完成解密和反序列化操作，避免主线程卡顿
                            NLogger.Log("Starting decryption and deserialization in background thread: {0}", arg0: settings.GetFullPath());
                            data = Storage.DecryptAndDeserialize(json, settings);
                            itemCount = data?.Count ?? 0;
                            success = true;
                            NLogger.Log("Background deserialization completed, {0} items processed: {1}", arg0: itemCount, arg1: settings.GetFullPath());
                        }
                    }
                    finally
                    {
                        fileOperationLock.ExitReadLock();
                        NLogger.Log("Read lock released for file: {0}", arg0: settings.GetFullPath());
                    }
                }
                catch (Exception ex)
                {
                    success = false;
                    errorMessage = ex.Message;
                    NLogger.LogError("Background thread error during file load: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                }

                // 将最终结果传递到主线程，避免在主线程进行耗时的反序列化操作
                Storage.RunOnMainThread(() =>
                {
                    if (success)
                    {
                        NLogger.Log("Loaded {0} items from file: {1}", arg0: itemCount, arg1: settings.GetFullPath());
                        callback?.Invoke(true, data);
                    }
                    else
                    {
                        if (errorMessage == "File is empty or does not exist")
                        {
                            NLogger.LogWarning("File is empty or does not exist: {0}", arg0: settings.GetFullPath());
                        }
                        else
                        {
                            NLogger.LogError("Failed to acquire read lock or load file: {0}", arg0: errorMessage);
                        }
                        callback?.Invoke(false, null, errorMessage);
                    }
                });
            });
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 格式化值用于日志输出，对集合类型进行特殊处理
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="value">要格式化的值</param>
        /// <param name="maxItems">集合类型最大显示项数</param>
        /// <param name="maxStringLength">单个项最大字符串长度</param>
        /// <returns>格式化后的字符串</returns>
        private static string FormatValueForLog<T>(T value, int maxItems = 10, int maxStringLength = 100)
        {
            if (value == null)
            {
                return "null";
            }

            Type type = typeof(T);

            // 处理字符串类型
            if (type == typeof(string))
            {
                string str = value as string;
                if (str.Length > maxStringLength)
                {
                    return $"{str.Substring(0, maxStringLength)}... (长度: {str.Length})";
                }
                return str;
            }

            // 处理基础类型和枚举
            if (type.IsPrimitive || type.IsEnum)
            {
                return value.ToString();
            }

            // 处理数组类型
            if (type.IsArray)
            {
                Array array = value as Array;
                if (array == null) return "null";

                int length = array.Length;
                if (length == 0) return "[]";

                System.Text.StringBuilder sb = new System.Text.StringBuilder();
                sb.Append("[");

                int count = System.Math.Min(length, maxItems);
                for (int i = 0; i < count; i++)
                {
                    object item = array.GetValue(i);
                    sb.Append(FormatSingleItem(item, maxStringLength));

                    if (i < count - 1)
                    {
                        sb.Append(", ");
                    }
                }

                if (length > maxItems)
                {
                    sb.Append($"... (共 {length} 项)");
                }

                sb.Append("]");
                return sb.ToString();
            }

            // 处理字典类型
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(System.Collections.Generic.Dictionary<,>))
            {
                var dictionary = value as System.Collections.IDictionary;
                if (dictionary == null) return "null";

                int count = dictionary.Count;
                if (count == 0) return "{}";

                System.Text.StringBuilder sb = new System.Text.StringBuilder();
                sb.Append("{");

                int i = 0;
                int displayCount = System.Math.Min(count, maxItems);
                foreach (System.Collections.DictionaryEntry entry in dictionary)
                {
                    if (i >= displayCount) break;

                    sb.Append(FormatSingleItem(entry.Key, maxStringLength));
                    sb.Append(": ");
                    sb.Append(FormatSingleItem(entry.Value, maxStringLength));

                    if (i < displayCount - 1)
                    {
                        sb.Append(", ");
                    }

                    i++;
                }

                if (count > maxItems)
                {
                    sb.Append($"... (共 {count} 项)");
                }

                sb.Append("}");
                return sb.ToString();
            }

            // 处理列表类型
            if (value is System.Collections.ICollection collection)
            {
                int count = collection.Count;
                if (count == 0) return "[]";

                System.Text.StringBuilder sb = new System.Text.StringBuilder();
                sb.Append("[");

                int i = 0;
                int displayCount = System.Math.Min(count, maxItems);
                foreach (object item in collection)
                {
                    if (i >= displayCount) break;

                    sb.Append(FormatSingleItem(item, maxStringLength));

                    if (i < displayCount - 1)
                    {
                        sb.Append(", ");
                    }

                    i++;
                }

                if (count > maxItems)
                {
                    sb.Append($"... (共 {count} 项)");
                }

                sb.Append("]");
                return sb.ToString();
            }

            // 处理其他复杂对象，避免ToString可能导致的性能问题
            try
            {
                // 尝试使用简单的ToString，但限制长度
                string result = value.ToString();
                if (result.Length > maxStringLength)
                {
                    return $"{result.Substring(0, maxStringLength)}... (类型: {type.Name})";
                }
                return result;
            }
            catch (Exception ex)
            {
                // 如果ToString方法出错，返回类型名称
                return $"<{type.Name}> (ToString错误: {ex.Message})";
            }
        }

        /// <summary>
        /// 格式化单个项用于日志输出
        /// </summary>
        /// <param name="item">要格式化的项</param>
        /// <param name="maxLength">最大字符串长度</param>
        /// <returns>格式化后的字符串</returns>
        private static string FormatSingleItem(object item, int maxLength)
        {
            if (item == null)
            {
                return "null";
            }

            // 处理字符串类型
            if (item is string str)
            {
                if (str.Length > maxLength)
                {
                    return $"\"{str.Substring(0, maxLength)}...\" (长度: {str.Length})";
                }
                return $"\"{str}\"";
            }

            // 处理基础类型和枚举
            Type itemType = item.GetType();
            if (itemType.IsPrimitive || itemType.IsEnum)
            {
                return item.ToString();
            }

            // 处理复杂类型
            try
            {
                string result = item.ToString();
                if (result.Length > maxLength)
                {
                    return $"{result.Substring(0, maxLength)}... (类型: {itemType.Name})";
                }
                return result;
            }
            catch
            {
                // 如果ToString方法出错，返回类型名称
                return $"<{itemType.Name}>";
            }
        }

        #endregion

        #region 释放资源

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_isDisposed)
            {
                // 等待所有文件操作完成
                if (_isFileOperationInProgress)
                {
                    NLogger.LogWarning("Instance [{0}] - Disposing while file operation [{1}] is in progress", arg0: _instanceId, arg1: _currentFileOperation);
                }

                // 释放读写锁
                try
                {
                    _fileOperationLock?.Dispose();
                }
                catch (Exception ex)
                {
                    NLogger.LogError("Error disposing file operation lock: {0}", arg0: ex.Message);
                }

                // 清理缓存
                _cache?.Clear();

                _isDisposed = true;
                NLogger.Log("Storage instance disposed: {0}", arg0: _instanceId);
            }
        }

        #endregion
    }
}
